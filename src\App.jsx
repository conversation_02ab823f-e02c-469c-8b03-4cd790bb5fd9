import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Box, CircularProgress } from '@mui/material'

import { useAuth } from './contexts/AuthContext'
import { auth } from './lib/firebase'
import Layout from './components/layout/Layout'
import LoginPage from './components/auth/LoginPage'
import OnboardingPage from './pages/OnboardingPage'
import Dashboard from './pages/Dashboard'
import StudyBlockPage from './pages/StudyBlockPage'
import FlashcardsPage from './pages/FlashcardsPage'
import MockTestLogPage from './pages/MockTestLogPage'
import CurrentAffairsPage from './pages/CurrentAffairsPage'
import DemoMode from './components/demo/DemoMode'

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress size={60} />
      </Box>
    )
  }

  if (!user) {
    return <Navigate to="/login" />
  }

  return children
}

// Onboarding Route - redirects to dashboard if onboarding completed
const OnboardingRoute = () => {
  const { userSettings, loading } = useAuth()

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress size={60} />
      </Box>
    )
  }

  if (userSettings?.onboardingCompleted) {
    return <Navigate to="/dashboard" />
  }

  return <OnboardingPage />
}

// Dashboard Route - shows demo mode if Firebase not configured, redirects to onboarding if not completed
const DashboardRoute = () => {
  const { userSettings, loading } = useAuth()

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress size={60} />
      </Box>
    )
  }

  // Show demo mode if Firebase is not configured
  if (!auth) {
    return <DemoMode />
  }

  if (!userSettings?.onboardingCompleted) {
    return <Navigate to="/onboarding" />
  }

  return <Dashboard />
}

function App() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress size={60} />
      </Box>
    )
  }

  return (
    <Routes>
      {/* Public Routes */}
      <Route
        path="/login"
        element={user ? <Navigate to="/dashboard" /> : <LoginPage />}
      />

      {/* Protected Routes */}
      <Route path="/" element={<ProtectedRoute><Layout /></ProtectedRoute>}>
        <Route index element={<Navigate to="/dashboard" />} />
        <Route path="onboarding" element={<OnboardingRoute />} />
        <Route path="dashboard" element={<DashboardRoute />} />
        <Route path="study/:blockId" element={<StudyBlockPage />} />
        <Route path="flashcards" element={<FlashcardsPage />} />
        <Route path="mock-tests" element={<MockTestLogPage />} />
        <Route path="current-affairs" element={<CurrentAffairsPage />} />
      </Route>

      {/* Fallback */}
      <Route path="*" element={<Navigate to="/dashboard" />} />
    </Routes>
  )
}

export default App
