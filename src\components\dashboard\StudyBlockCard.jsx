import React, { useState } from 'react'
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  Box,
  IconButton,
  Tooltip,
  Stack,
  <PERSON>lapse,
  Divider,
} from '@mui/material'
import {
  PlayArrow as PlayIcon,
  CheckCircle as CompleteIcon,
  Info as InfoIcon,
  ExpandMore as ExpandIcon,
  Schedule as TimeIcon,
  TrendingUp as DifficultyIcon,
} from '@mui/icons-material'

import { getBloomLevel } from '../../data/bloomLevels'

const StudyBlockCard = ({ block, onStart, onComplete }) => {
  const [expanded, setExpanded] = useState(false)
  const [completed, setCompleted] = useState(false)

  const bloomLevel = getBloomLevel(block.bloomLevel)
  
  const getTypeColor = (type) => {
    switch (type) {
      case 'concept': return 'primary'
      case 'review': return 'secondary'
      case 'mixed': return 'warning'
      case 'booster': return 'error'
      default: return 'default'
    }
  }

  const getTypeIcon = (type) => {
    switch (type) {
      case 'concept': return '📚'
      case 'review': return '🔄'
      case 'mixed': return '🔀'
      case 'booster': return '🚀'
      default: return '📖'
    }
  }

  const handleComplete = () => {
    setCompleted(true)
    onComplete?.(block.id)
  }

  const handleStart = () => {
    onStart?.(block)
  }

  return (
    <Card 
      elevation={2}
      sx={{ 
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        opacity: completed ? 0.7 : 1,
        border: completed ? '2px solid' : 'none',
        borderColor: completed ? 'success.main' : 'transparent',
      }}
    >
      <CardContent sx={{ flexGrow: 1 }}>
        {/* Header */}
        <Stack direction="row" alignItems="flex-start" spacing={1} sx={{ mb: 2 }}>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {getTypeIcon(block.type)} {block.title}
          </Typography>
          <Tooltip title="Block details">
            <IconButton 
              size="small" 
              onClick={() => setExpanded(!expanded)}
              sx={{ 
                transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                transition: 'transform 0.3s'
              }}
            >
              <ExpandIcon />
            </IconButton>
          </Tooltip>
        </Stack>

        {/* Type and Bloom Level */}
        <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
          <Chip 
            label={block.type?.toUpperCase() || 'CONCEPT'}
            color={getTypeColor(block.type)}
            size="small"
          />
          <Chip
            label={bloomLevel.name}
            size="small"
            sx={{
              backgroundColor: bloomLevel.color,
              color: bloomLevel.textColor,
            }}
          />
        </Stack>

        {/* Quick Info */}
        <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
          <Box display="flex" alignItems="center" gap={0.5}>
            <TimeIcon fontSize="small" color="action" />
            <Typography variant="body2" color="text.secondary">
              1.5 hours
            </Typography>
          </Box>
          <Box display="flex" alignItems="center" gap={0.5}>
            <DifficultyIcon fontSize="small" color="action" />
            <Typography variant="body2" color="text.secondary">
              Level {block.difficulty || 1}
            </Typography>
          </Box>
        </Stack>

        {/* Rationale */}
        {block.rationale && (
          <Box sx={{ mb: 2 }}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <InfoIcon fontSize="small" color="primary" />
              <Typography variant="body2" color="primary">
                Why this block today?
              </Typography>
            </Stack>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              {block.rationale}
            </Typography>
          </Box>
        )}

        {/* Expandable Details */}
        <Collapse in={expanded}>
          <Divider sx={{ my: 2 }} />
          
          {/* Pre-Study Tasks */}
          {block.preStudyTasks && block.preStudyTasks.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.primary" gutterBottom>
                📋 Pre-Study Preparation
              </Typography>
              {block.preStudyTasks.map((task, index) => (
                <Typography key={index} variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  • {task}
                </Typography>
              ))}
            </Box>
          )}

          {/* Study Tasks */}
          {block.studyTasks && block.studyTasks.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.primary" gutterBottom>
                📖 Study Activities
              </Typography>
              {block.studyTasks.map((task, index) => (
                <Typography key={index} variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  • {task}
                </Typography>
              ))}
            </Box>
          )}

          {/* Post-Study Tasks */}
          {block.postStudyTasks && block.postStudyTasks.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.primary" gutterBottom>
                ✅ Post-Study Review
              </Typography>
              {block.postStudyTasks.map((task, index) => (
                <Typography key={index} variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  • {task}
                </Typography>
              ))}
            </Box>
          )}

          {/* Tags */}
          {block.tags && block.tags.length > 0 && (
            <Box>
              <Typography variant="subtitle2" color="text.primary" gutterBottom>
                🏷️ Tags
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap">
                {block.tags.map((tag, index) => (
                  <Chip 
                    key={index}
                    label={tag}
                    size="small"
                    variant="outlined"
                  />
                ))}
              </Stack>
            </Box>
          )}
        </Collapse>
      </CardContent>

      {/* Actions */}
      <CardActions sx={{ p: 2, pt: 0 }}>
        {completed ? (
          <Button
            fullWidth
            variant="contained"
            color="success"
            startIcon={<CompleteIcon />}
            disabled
          >
            Completed
          </Button>
        ) : (
          <Stack direction="row" spacing={1} width="100%">
            <Button
              fullWidth
              variant="contained"
              startIcon={<PlayIcon />}
              onClick={handleStart}
              size="large"
              sx={{
                minHeight: 56,
                fontSize: '1.1rem',
                fontWeight: 'bold'
              }}
            >
              START
            </Button>
            <Button
              variant="outlined"
              startIcon={<CompleteIcon />}
              onClick={handleComplete}
            >
              Mark Complete
            </Button>
          </Stack>
        )}
      </CardActions>
    </Card>
  )
}

export default StudyBlockCard
