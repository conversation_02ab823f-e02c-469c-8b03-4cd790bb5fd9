import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { ThemeProvider } from '@mui/material/styles'
import { CssBaseline, Box, Button } from '@mui/material'

import theme from './theme.js'
import { AuthProvider, useAuth } from './contexts/AuthContext.jsx'

// Simple working app with auth
const App = () => {
  const { user, loading, signInWithGoogle } = useAuth()

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <h1>🎯 LogosPath Study Scheduler</h1>
        <p>🔄 Loading...</p>
      </Box>
    )
  }

  if (!user) {
    return (
      <Box sx={{ p: 3 }}>
        <h1>🎯 LogosPath Study Scheduler</h1>
        <p>✅ React is working!</p>
        <p>✅ Material-UI is working!</p>
        <p>✅ Firebase is configured!</p>
        <Button
          variant="contained"
          onClick={signInWithGoogle}
          sx={{ mt: 2 }}
        >
          Sign In with Google
        </Button>
      </Box>
    )
  }

  return (
    <Box sx={{ p: 3 }}>
      <h1>🎯 LogosPath Study Scheduler</h1>
      <p>✅ Welcome, {user.email}!</p>

      {/* Simple Dashboard */}
      <Box sx={{ mt: 4 }}>
        <h2>📚 Today's Study Plan</h2>
        <Box sx={{ display: 'grid', gap: 2, gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))' }}>

          {/* Study Block 1 */}
          <Box sx={{ p: 2, border: '1px solid #ddd', borderRadius: 2 }}>
            <h3>📖 Introduction to Percentage</h3>
            <p><strong>Duration:</strong> 1.5 hours</p>
            <p><strong>Type:</strong> Concept Learning</p>
            <Button variant="contained" size="large" sx={{ mt: 1 }}>
              START
            </Button>
          </Box>

          {/* Study Block 2 */}
          <Box sx={{ p: 2, border: '1px solid #ddd', borderRadius: 2 }}>
            <h3>🔄 Simple Interest Review</h3>
            <p><strong>Duration:</strong> 1.5 hours</p>
            <p><strong>Type:</strong> Review (Delta-2)</p>
            <Button variant="contained" size="large" sx={{ mt: 1 }}>
              START
            </Button>
          </Box>

          {/* Study Block 3 */}
          <Box sx={{ p: 2, border: '1px solid #ddd', borderRadius: 2 }}>
            <h3>📊 Data Interpretation</h3>
            <p><strong>Duration:</strong> 1.5 hours</p>
            <p><strong>Type:</strong> Practice</p>
            <Button variant="contained" size="large" sx={{ mt: 1 }}>
              START
            </Button>
          </Box>

        </Box>
      </Box>

      {/* Quick Navigation */}
      <Box sx={{ mt: 4 }}>
        <h2>🚀 Quick Access</h2>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button variant="outlined">📚 Flashcards</Button>
          <Button variant="outlined">📊 Mock Tests</Button>
          <Button variant="outlined">📰 Current Affairs</Button>
          <Button variant="outlined">🎯 Multi-Exam Manager</Button>
        </Box>
      </Box>
    </Box>
  )
}

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AuthProvider>
          <App />
        </AuthProvider>
      </ThemeProvider>
    </BrowserRouter>
  </React.StrictMode>
)
