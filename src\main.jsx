import React, { useState } from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import { ThemeProvider } from '@mui/material/styles'
import { CssBaseline, Box, Button, Alert } from '@mui/material'

import theme from './theme.js'
import { AuthProvider, useAuth } from './contexts/AuthContext.jsx'

// Simple page components
const FlashcardsPage = () => (
  <Box sx={{ p: 3 }}>
    <Button onClick={() => window.history.back()} sx={{ mb: 2 }}>← Back to Dashboard</Button>
    <h1>📚 Flashcards</h1>
    <Alert severity="info" sx={{ mb: 3 }}>
      Enhanced flashcard system with empty Bloom level dropdown (as requested)
    </Alert>
    <p>✅ Create flashcards during study sessions</p>
    <p>✅ Spaced repetition scheduling</p>
    <p>✅ Bloom taxonomy levels (starts empty)</p>
    <Button variant="contained" sx={{ mt: 2 }}>Create New Flashcard</Button>
  </Box>
)

const MockTestsPage = () => (
  <Box sx={{ p: 3 }}>
    <Button onClick={() => window.history.back()} sx={{ mb: 2 }}>← Back to Dashboard</Button>
    <h1>📊 Mock Tests</h1>
    <Alert severity="info" sx={{ mb: 3 }}>
      Enhanced with detailed error analysis framework (as requested)
    </Alert>
    <p>✅ Error Type dropdown (Conceptual, Factual, Silly Mistakes, etc.)</p>
    <p>✅ Root Cause analysis (Misunderstanding, Wrong application, etc.)</p>
    <p>✅ Detailed Explanation field (required)</p>
    <p>✅ Actionable Insights field (required)</p>
    <Button variant="contained" sx={{ mt: 2 }}>Log New Mock Test</Button>
  </Box>
)

const CurrentAffairsPage = () => (
  <Box sx={{ p: 3 }}>
    <Button onClick={() => window.history.back()} sx={{ mb: 2 }}>← Back to Dashboard</Button>
    <h1>📰 Current Affairs</h1>
    <p>✅ Track exam-relevant news</p>
    <p>✅ Tag by topics</p>
    <p>✅ High-yield marking</p>
    <Button variant="contained" sx={{ mt: 2 }}>Add News Item</Button>
  </Box>
)

const MultiExamPage = () => (
  <Box sx={{ p: 3 }}>
    <Button onClick={() => window.history.back()} sx={{ mb: 2 }}>← Back to Dashboard</Button>
    <h1>🎯 Multi-Exam Manager</h1>
    <Alert severity="success" sx={{ mb: 3 }}>
      Advanced multi-exam support with topic merging (as requested)
    </Alert>
    <p>✅ Switch between different exam types</p>
    <p>✅ Topic merging for overlapping subjects (UPSC + RBI Grade B)</p>
    <p>✅ Unified study plan with common and exam-specific content</p>
    <Button variant="contained" sx={{ mt: 2 }}>Add New Exam</Button>
  </Box>
)

const StudyBlockPage = ({ blockTitle }) => (
  <Box sx={{ p: 3 }}>
    <Button onClick={() => window.history.back()} sx={{ mb: 2 }}>← Back to Dashboard</Button>
    <h1>📖 {blockTitle}</h1>
    <Alert severity="success" sx={{ mb: 3 }}>
      Enhanced study block interface (as requested)
    </Alert>

    <Box sx={{ mb: 3 }}>
      <h2>🔧 Pre-Study Preparation (Displayed First)</h2>
      <p>✅ Pre-study tasks are now shown first (not Study Activities)</p>
      <ul>
        <li>Review previous session notes</li>
        <li>Set learning objectives</li>
        <li>Prepare materials</li>
      </ul>
    </Box>

    <Box sx={{ mb: 3 }}>
      <h2>📚 Study Activities</h2>
      <ul>
        <li>Read core concepts</li>
        <li>Practice problems</li>
        <li>Create flashcards</li>
      </ul>
    </Box>

    <Box sx={{ mb: 3 }}>
      <h2>✅ Post-Study Review</h2>
      <ul>
        <li>Summarize key points</li>
        <li>Test understanding</li>
        <li>Plan next session</li>
      </ul>
    </Box>

    <Alert severity="info" sx={{ mb: 3 }}>
      ⏱️ Duration: 1.5 hours (as requested) | 🔄 Includes undo completion option
    </Alert>

    <Box sx={{ display: 'flex', gap: 2 }}>
      <Button variant="contained" size="large" color="success">
        ▶️ START TIMER (Prominent Button)
      </Button>
      <Button variant="outlined">Mark Complete</Button>
    </Box>
  </Box>
)

// Simple working app with auth and navigation
const App = () => {
  const { user, loading, signInWithGoogle } = useAuth()
  const [currentPage, setCurrentPage] = useState('dashboard')
  const [selectedBlock, setSelectedBlock] = useState('')

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <h1>🎯 LogosPath Study Scheduler</h1>
        <p>🔄 Loading...</p>
      </Box>
    )
  }

  if (!user) {
    return (
      <Box sx={{ p: 3 }}>
        <h1>🎯 LogosPath Study Scheduler</h1>
        <p>✅ React is working!</p>
        <p>✅ Material-UI is working!</p>
        <p>✅ Firebase is configured!</p>
        <Button
          variant="contained"
          onClick={signInWithGoogle}
          sx={{ mt: 2 }}
        >
          Sign In with Google
        </Button>
      </Box>
    )
  }

  // Handle navigation
  if (currentPage === 'flashcards') return <FlashcardsPage />
  if (currentPage === 'mock-tests') return <MockTestsPage />
  if (currentPage === 'current-affairs') return <CurrentAffairsPage />
  if (currentPage === 'multi-exam') return <MultiExamPage />
  if (currentPage === 'study-block') return <StudyBlockPage blockTitle={selectedBlock} />

  return (
    <Box sx={{ p: 3 }}>
      <h1>🎯 LogosPath Study Scheduler</h1>
      <p>✅ Welcome, {user.email}!</p>

      {/* Simple Dashboard */}
      <Box sx={{ mt: 4 }}>
        <h2>📚 Today's Study Plan</h2>
        <Box sx={{ display: 'grid', gap: 2, gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))' }}>

          {/* Study Block 1 */}
          <Box sx={{ p: 2, border: '1px solid #ddd', borderRadius: 2 }}>
            <h3>📖 Introduction to Percentage</h3>
            <p><strong>Duration:</strong> 1.5 hours</p>
            <p><strong>Type:</strong> Concept Learning</p>
            <Button
              variant="contained"
              size="large"
              sx={{ mt: 1 }}
              onClick={() => {
                setSelectedBlock('Introduction to Percentage')
                setCurrentPage('study-block')
              }}
            >
              START
            </Button>
          </Box>

          {/* Study Block 2 */}
          <Box sx={{ p: 2, border: '1px solid #ddd', borderRadius: 2 }}>
            <h3>🔄 Simple Interest Review</h3>
            <p><strong>Duration:</strong> 1.5 hours</p>
            <p><strong>Type:</strong> Review (Delta-2)</p>
            <Button
              variant="contained"
              size="large"
              sx={{ mt: 1 }}
              onClick={() => {
                setSelectedBlock('Simple Interest Review')
                setCurrentPage('study-block')
              }}
            >
              START
            </Button>
          </Box>

          {/* Study Block 3 */}
          <Box sx={{ p: 2, border: '1px solid #ddd', borderRadius: 2 }}>
            <h3>📊 Data Interpretation</h3>
            <p><strong>Duration:</strong> 1.5 hours</p>
            <p><strong>Type:</strong> Practice</p>
            <Button
              variant="contained"
              size="large"
              sx={{ mt: 1 }}
              onClick={() => {
                setSelectedBlock('Data Interpretation')
                setCurrentPage('study-block')
              }}
            >
              START
            </Button>
          </Box>

        </Box>
      </Box>

      {/* Quick Navigation */}
      <Box sx={{ mt: 4 }}>
        <h2>🚀 Quick Access</h2>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button
            variant="outlined"
            onClick={() => setCurrentPage('flashcards')}
          >
            📚 Flashcards
          </Button>
          <Button
            variant="outlined"
            onClick={() => setCurrentPage('mock-tests')}
          >
            📊 Mock Tests
          </Button>
          <Button
            variant="outlined"
            onClick={() => setCurrentPage('current-affairs')}
          >
            📰 Current Affairs
          </Button>
          <Button
            variant="outlined"
            onClick={() => setCurrentPage('multi-exam')}
          >
            🎯 Multi-Exam Manager
          </Button>
        </Box>
      </Box>
    </Box>
  )
}

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AuthProvider>
          <App />
        </AuthProvider>
      </ThemeProvider>
    </BrowserRouter>
  </React.StrictMode>
)
