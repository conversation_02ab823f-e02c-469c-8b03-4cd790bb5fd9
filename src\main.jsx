import React from 'react'
import ReactDOM from 'react-dom/client'

// Ultra minimal test
console.log('🔥 main.jsx is loading...')

const TestApp = () => {
  console.log('🔥 TestApp is rendering...')
  return React.createElement('div', { style: { padding: '20px', backgroundColor: 'lightblue' } },
    React.createElement('h1', null, 'REACT IS WORKING!'),
    React.createElement('p', null, 'If you see this, React is successfully rendering.')
  )
}

console.log('🔥 About to create root...')
const root = ReactDOM.createRoot(document.getElementById('root'))
console.log('🔥 Root created, about to render...')

root.render(React.createElement(TestApp))
console.log('🔥 Render called!')
